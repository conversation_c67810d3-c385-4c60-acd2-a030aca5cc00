import { api } from './AuthService';
import { firebaseService } from './FirebaseService';

/**
 * Cross-Platform Call Service for managing calls between web and mobile platforms
 * This service integrates with the backend calls/send-call endpoint and handles FCM notifications
 */
export class CrossPlatformCallService {
  constructor() {
    this.pendingCalls = new Map(); // Track pending calls
  }

  /**
   * Initiate a cross-platform call using the calls/send-call endpoint
   * @param {Object} callParams - Call parameters
   * @param {string} callParams.callerId - Caller's user ID (users.id)
   * @param {string} callParams.calleeId - Callee's user ID (users.id)
   * @param {string} callParams.eventCode - Event code from eventData
   * @param {string} callParams.channelName - Agora channel name
   * @param {boolean} callParams.isVideoCall - Whether this is a video call
   * @param {string} callParams.callerName - Caller's display name
   * @returns {Promise<Object>} Call details including tokens and FCM information
   */
  async initiateCall(callParams) {
    try {
      const {
        callerId,
        calleeId,
        eventCode,
        channelName,
        isVideoCall,
        callerName
      } = callParams;

      console.log('Initiating cross-platform call with params:', callParams);

      // Call the backend calls/send-call endpoint
      const response = await api.post('/calls/send-call', {
        callerId: callerId, // Use users.id instead of agoraId
        calleeId: calleeId, // Use users.id instead of agoraId
        eventCode: eventCode,
        channelName: channelName,
        isVideoCall: isVideoCall,
        callerName: callerName,
        timestamp: Date.now()
      });

      if (!response.data) {
        throw new Error('Invalid response from calls/send-call endpoint');
      }

      const callDetails = response.data;
      console.log('Call details received:', callDetails);

      // Expected response structure:
      // {
      //   callerFCMToken: "caller's FCM token",
      //   calleeFCMToken: "callee's FCM token", 
      //   rtcToken: "RTC token for the channel",
      //   jwtToken: "JWT token for authentication",
      //   callId: "unique call identifier",
      //   success: true
      // }

      // Store call details for tracking
      const callId = callDetails.callId || `call_${Date.now()}`;
      this.pendingCalls.set(callId, {
        ...callDetails,
        ...callParams,
        callId,
        startTime: Date.now(),
        status: 'initiated'
      });

      // If callee has FCM token, send push notification for mobile users
      if (callDetails.calleeFCMToken) {
        console.log('Sending FCM notification to mobile device');
        await this.sendMobileCallNotification(callDetails.calleeFCMToken, {
          callId,
          callerId,
          callerName,
          channelName,
          isVideoCall,
          timestamp: Date.now()
        });
      }

      return {
        success: true,
        callId,
        rtcToken: callDetails.rtcToken,
        jwtToken: callDetails.jwtToken,
        callerFCMToken: callDetails.callerFCMToken,
        calleeFCMToken: callDetails.calleeFCMToken,
        channelName,
        isVideoCall
      };

    } catch (error) {
      console.error('Error initiating cross-platform call:', error);
      throw new Error(`Failed to initiate call: ${error.message}`);
    }
  }

  /**
   * Send FCM notification to mobile device
   * @param {string} fcmToken - Target device FCM token
   * @param {Object} callData - Call information
   * @returns {Promise<boolean>} Success status
   */
  async sendMobileCallNotification(fcmToken, callData) {
    try {
      // Send FCM notification via backend
      // Note: FCM notifications must be sent from the backend for security reasons
      const response = await api.post('/calls/send-fcm-notification', {
        targetToken: fcmToken,
        callData: callData,
        notificationType: 'call_request'
      });

      if (response.data && response.data.success) {
        console.log('FCM notification sent successfully');
        return true;
      } else {
        console.error('Failed to send FCM notification:', response.data);
        return false;
      }
    } catch (error) {
      console.error('Error sending FCM notification:', error);
      
      // Fallback: Use Firebase service (though this won't work due to security restrictions)
      // This is mainly for development/testing purposes
      return await firebaseService.sendCallNotification(fcmToken, callData);
    }
  }

  /**
   * Handle call acceptance from mobile device
   * @param {string} callId - Call identifier
   * @param {string} accepterId - ID of user accepting the call
   */
  async handleCallAccepted(callId, accepterId) {
    const callDetails = this.pendingCalls.get(callId);
    if (callDetails) {
      callDetails.status = 'accepted';
      callDetails.acceptedAt = Date.now();
      callDetails.accepterId = accepterId;
      
      console.log('Call accepted:', callDetails);
      
      // Notify other components about call acceptance
      this.notifyCallAccepted(callDetails);
    }
  }

  /**
   * Handle call rejection from mobile device
   * @param {string} callId - Call identifier
   * @param {string} rejecterId - ID of user rejecting the call
   */
  async handleCallRejected(callId, rejecterId) {
    const callDetails = this.pendingCalls.get(callId);
    if (callDetails) {
      callDetails.status = 'rejected';
      callDetails.rejectedAt = Date.now();
      callDetails.rejecterId = rejecterId;
      
      console.log('Call rejected:', callDetails);
      
      // Clean up pending call
      this.pendingCalls.delete(callId);
      
      // Notify other components about call rejection
      this.notifyCallRejected(callDetails);
    }
  }

  /**
   * Handle call cancellation
   * @param {string} callId - Call identifier
   */
  async cancelCall(callId) {
    const callDetails = this.pendingCalls.get(callId);
    if (callDetails) {
      callDetails.status = 'cancelled';
      callDetails.cancelledAt = Date.now();
      
      console.log('Call cancelled:', callDetails);
      
      // Send cancellation notification to mobile device if needed
      if (callDetails.calleeFCMToken) {
        await this.sendCallCancellationNotification(callDetails.calleeFCMToken, callId);
      }
      
      // Clean up pending call
      this.pendingCalls.delete(callId);
      
      // Notify other components about call cancellation
      this.notifyCallCancelled(callDetails);
    }
  }

  /**
   * Send call cancellation notification
   * @param {string} fcmToken - Target device FCM token
   * @param {string} callId - Call identifier
   */
  async sendCallCancellationNotification(fcmToken, callId) {
    try {
      await api.post('/calls/send-fcm-notification', {
        targetToken: fcmToken,
        callData: { callId },
        notificationType: 'call_cancelled'
      });
    } catch (error) {
      console.error('Error sending call cancellation notification:', error);
    }
  }

  /**
   * Get call details by ID
   * @param {string} callId - Call identifier
   * @returns {Object|null} Call details or null if not found
   */
  getCallDetails(callId) {
    return this.pendingCalls.get(callId) || null;
  }

  /**
   * Get all pending calls
   * @returns {Array} Array of pending call details
   */
  getPendingCalls() {
    return Array.from(this.pendingCalls.values());
  }

  /**
   * Clear all pending calls
   */
  clearPendingCalls() {
    this.pendingCalls.clear();
  }

  // Event notification methods (to be implemented based on your event system)
  notifyCallAccepted(callDetails) {
    // Implement based on your event system
    console.log('Call accepted notification:', callDetails);
  }

  notifyCallRejected(callDetails) {
    // Implement based on your event system
    console.log('Call rejected notification:', callDetails);
  }

  notifyCallCancelled(callDetails) {
    // Implement based on your event system
    console.log('Call cancelled notification:', callDetails);
  }
}

// Export singleton instance
export const crossPlatformCallService = new CrossPlatformCallService();
